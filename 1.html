<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>南京五日轻松亲子游攻略表</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap');

        :root {
            --primary-color: #667eea;
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-color: #f093fb;
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent-color: #4facfe;
            --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --success-color: #11998e;
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --warning-color: #f093fb;
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --text-muted: #718096;
            --bg-primary: #ffffff;
            --bg-secondary: #f7fafc;
            --bg-tertiary: #edf2f7;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @media print {
            @page {
                size: A4;
                margin: 10mm;
            }
            body {
                width: 210mm;
                height: 297mm;
                background: white !important;
            }
            .page-break {
                page-break-before: always;
            }
            .no-break {
                page-break-inside: avoid;
            }
            .print-button {
                display: none !important;
            }
            * {
                box-shadow: none !important;
                background: white !important;
                color: black !important;
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--text-primary);
            line-height: 1.7;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: var(--bg-primary);
            box-shadow: var(--shadow-xl);
            border-radius: var(--border-radius-lg);
            margin-top: 2rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            z-index: 1;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 3rem 2rem;
            background: var(--primary-gradient);
            border-radius: var(--border-radius-lg);
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            letter-spacing: -0.02em;
            position: relative;
            z-index: 2;
        }

        .header-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .header-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
            position: relative;
            z-index: 2;
        }

        .header-info-item {
            background: rgba(255, 255, 255, 0.15);
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: var(--transition);
        }

        .header-info-item:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }

        .header-info-item i {
            font-size: 1.2rem;
            margin-right: 0.75rem;
            opacity: 0.9;
        }
        
        .section-title {
            background: var(--accent-gradient);
            color: white;
            padding: 1.5rem 2rem;
            margin: 3rem 0 2rem 0;
            border-radius: var(--border-radius);
            font-size: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
            transition: var(--transition);
        }

        .section-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .section-title:hover::before {
            left: 100%;
        }

        .section-title i {
            margin-right: 1rem;
            font-size: 1.3rem;
            opacity: 0.9;
        }

        .day-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem 2rem;
            margin: 2.5rem 0 1.5rem 0;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: 1.3rem;
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        .day-header::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1));
            transform: skewX(-15deg);
        }

        .day-header i {
            margin-right: 1rem;
            opacity: 0.9;
        }
        
        .itinerary-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-bottom: 2rem;
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .itinerary-table th {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--text-primary);
            padding: 1.25rem 1.5rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.95rem;
            letter-spacing: 0.025em;
            text-transform: uppercase;
            border-bottom: 2px solid var(--border-color);
            position: relative;
        }

        .itinerary-table th:first-child {
            border-top-left-radius: var(--border-radius);
        }

        .itinerary-table th:last-child {
            border-top-right-radius: var(--border-radius);
        }

        .itinerary-table td {
            padding: 1.5rem;
            vertical-align: top;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
        }

        .itinerary-table tr:hover td {
            background: var(--bg-secondary);
        }

        .itinerary-table tr:last-child td {
            border-bottom: none;
        }

        .itinerary-table tr:last-child td:first-child {
            border-bottom-left-radius: var(--border-radius);
        }

        .itinerary-table tr:last-child td:last-child {
            border-bottom-right-radius: var(--border-radius);
        }

        .time-col {
            width: 15%;
            font-weight: 600;
            color: var(--accent-color);
            font-size: 0.95rem;
        }

        .activity-col {
            width: 25%;
            font-weight: 500;
            color: var(--text-primary);
        }

        .details-col {
            width: 60%;
            color: var(--text-secondary);
            line-height: 1.6;
        }
        
        .info-card {
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            padding: 1.5rem 2rem;
            margin: 1.5rem 0;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .info-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .info-card::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            transition: var(--transition);
        }

        .transport-box::before {
            background: var(--warning-gradient);
        }

        .food-box::before {
            background: var(--success-gradient);
        }

        .tip-box {
            background: linear-gradient(135deg, #fff8e1 0%, #ffeaa7 100%);
            border: 1px solid #fdcb6e;
        }

        .tip-box::before {
            background: var(--warning-gradient);
        }

        .accommodation-box::before {
            background: var(--secondary-gradient);
        }

        .info-card h4 {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 0.75rem;
            font-size: 1.1rem;
        }

        .info-card p {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 0.5rem;
        }

        .info-card p:last-child {
            margin-bottom: 0;
        }
        
        .contact-box {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            margin: 2rem 0;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
        }

        .contact-section h4 {
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .contact-section p {
            margin-bottom: 0.5rem;
            opacity: 0.8;
        }

        .food-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .food-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .food-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-4px);
        }

        .food-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--success-gradient);
        }

        .food-card h4 {
            color: var(--text-primary);
            margin-bottom: 1rem;
            font-weight: 600;
            font-size: 1.2rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid var(--border-color);
        }

        .food-card p {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 0.75rem;
        }

        .food-card p:last-child {
            color: var(--success-color);
            font-weight: 600;
            margin-bottom: 0;
        }
        
        .checklist {
            list-style: none;
            margin: 1.5rem 0;
            display: grid;
            gap: 0.75rem;
        }

        .checklist li {
            padding: 1rem 1rem 1rem 3rem;
            position: relative;
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .checklist li:hover {
            background: var(--bg-tertiary);
            transform: translateX(4px);
        }

        .checklist li:before {
            content: "\f00c";
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--success-color);
            font-size: 1.1rem;
        }

        .budget-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-bottom: 2rem;
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .budget-table th, .budget-table td {
            padding: 1.25rem 1.5rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .budget-table th {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.95rem;
            letter-spacing: 0.025em;
        }

        .budget-table td {
            color: var(--text-secondary);
        }

        .budget-table tr:last-child td {
            border-bottom: none;
        }

        .budget-total {
            font-weight: 700;
            background: var(--success-gradient);
            color: white;
        }

        .budget-total td {
            color: white;
            font-size: 1.1rem;
        }
        
        .print-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 2rem auto;
            padding: 1rem 2rem;
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            font-weight: 600;
            font-size: 1rem;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .print-button:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .print-button:active {
            transform: translateY(0);
        }

        .print-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .print-button:hover::before {
            left: 100%;
        }

        .print-button i {
            margin-right: 0.5rem;
        }

        .icon {
            margin-right: 0.5rem;
            opacity: 0.9;
        }

        .legend {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
            padding: 1.5rem;
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .legend-item {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 0.75rem;
            border-radius: 4px;
            box-shadow: var(--shadow-sm);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 1rem;
                padding: 1rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .header-info {
                grid-template-columns: 1fr;
            }

            .food-section {
                grid-template-columns: 1fr;
            }

            .contact-box {
                grid-template-columns: 1fr;
            }

            .legend {
                grid-template-columns: 1fr;
            }
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--accent-color);
        }
    </style>
</head>
<body>
    <button class="print-button" onclick="window.print()">
        <i class="fas fa-print"></i>打印行程表
    </button>

    <div class="container">
        <div class="header">
            <h1>南京五日轻松亲子游攻略表</h1>
            <p class="header-subtitle">精心规划的文化探索之旅，适合全家共享的美好时光</p>
            <div class="header-info">
                <div class="header-info-item">
                    <i class="fas fa-calendar-alt icon"></i>
                    <span>日期：2024年6月15日 - 6月19日</span>
                </div>
                <div class="header-info-item">
                    <i class="fas fa-users icon"></i>
                    <span>出行人数：3人（含60岁左右父母）</span>
                </div>
                <div class="header-info-item">
                    <i class="fas fa-thermometer-half icon"></i>
                    <span>预计天气：25-32°C，多云偶有阵雨</span>
                </div>
            </div>
        </div>

    <div class="section-title">
        <i class="fas fa-map-marked-alt"></i>行程概览
    </div>
    <table class="itinerary-table">
        <tr>
            <th>日期</th>
            <th>行程</th>
            <th>住宿</th>
        </tr>
        <tr>
            <td>第一天<br>6月15日(周六)</td>
            <td>合肥→南京，抵达后游览老门东、夫子庙</td>
            <td rowspan="5">锦江之星/如家酒店<br>（新街口或夫子庙地区）<br>双床房/标准间<br>预算：400-500元/晚</td>
        </tr>
        <tr>
            <td>第二天<br>6月16日(周日)</td>
            <td>南京博物院、总统府</td>
        </tr>
        <tr>
            <td>第三天<br>6月17日(周一)</td>
            <td>明孝陵、美龄宫/中山陵</td>
        </tr>
        <tr>
            <td>第四天<br>6月18日(周二)</td>
            <td>瞻园、江宁织造博物馆</td>
        </tr>
        <tr>
            <td>第五天<br>6月19日(周三)</td>
            <td>玄武湖公园，返回合肥</td>
        </tr>
    </table>

        <div class="section-title">
            <i class="fas fa-train"></i>交通信息
        </div>
        <div class="info-card transport-box">
            <h4><i class="fas fa-arrow-right icon"></i>去程</h4>
            <p><strong>合肥南站 → 南京南站</strong></p>
            <p>G103次列车 (8:08-9:06)，二等座：97.5元/人，一等座：162.5元/人</p>
        </div>
        <div class="info-card transport-box">
            <h4><i class="fas fa-arrow-left icon"></i>返程</h4>
            <p><strong>南京南站 → 合肥南站</strong></p>
            <p>G411次列车 (15:04-16:01)，二等座：97.5元/人，一等座：162.5元/人</p>
        </div>
        <div class="info-card transport-box">
            <h4><i class="fas fa-subway icon"></i>市内交通</h4>
            <p><strong>地铁：</strong>覆盖主要景点，老人票价优惠50%</p>
            <p><strong>公交：</strong>便捷经济，老人免费乘坐</p>
            <p><strong>出租车：</strong>起步价14元，适合短途或疲劳时使用</p>
        </div>

    <div class="page-break"></div>

    <div class="section-title">
        <i class="fas fa-calendar-day"></i>每日详细行程
    </div>

    <div class="day-header">
        <i class="fas fa-calendar-day"></i>第一天 (6月15日 周六)：抵达南京，初识六朝古都
    </div>
    <table class="itinerary-table no-break">
        <tr>
            <th class="time-col">时间</th>
            <th class="activity-col">活动</th>
            <th class="details-col">详情</th>
        </tr>
        <tr>
            <td>08:08-09:06</td>
            <td>乘坐高铁</td>
            <td>
                <p>合肥南站→南京南站 G103次列车</p>
                <p>到站后可先寄存行李或前往酒店</p>
            </td>
        </tr>
        <tr>
            <td>10:00-12:00</td>
            <td>酒店办理入住</td>
            <td>
                <p>建议入住锦江之星/如家酒店（新街口或夫子庙附近）</p>
                <p>提前与酒店确认是否可提前入住，或可先寄存行李</p>
            </td>
        </tr>
        <tr>
            <td>12:00-13:30</td>
            <td>午餐</td>
            <td>
                <p>推荐：南京大牌档（夫子庙店）</p>
                <p>品尝：盐水鸭、鸭血粉丝汤（不辣）</p>
                <p>人均：80-100元</p>
            </td>
        </tr>
        <tr>
            <td>14:00-17:00</td>
            <td>老门东历史文化街区</td>
            <td>
                <p>明清风格建筑，传统与现代融合的步行街</p>
                <p>建议：缓慢闲逛，欣赏传统建筑，可适当休息</p>
                <p>亮点：非遗展示馆、科举博物馆（免费）</p>
            </td>
        </tr>
        <tr>
            <td>17:30-19:00</td>
            <td>晚餐</td>
            <td>
                <p>推荐：秦淮人家（夫子庙店）</p>
                <p>品尝：清炖蹄膀、糯米蒸饺、桂花拉糕（清淡不辣）</p>
                <p>人均：80-100元</p>
            </td>
        </tr>
        <tr>
            <td>19:30-21:00</td>
            <td>夫子庙夜景</td>
            <td>
                <p>欣赏秦淮河夜景，漫步古街</p>
                <p>可选：秦淮河游船（60-80元/人，30分钟）</p>
                <p>根据父母体力情况安排是否游船</p>
            </td>
        </tr>
    </table>

        <div class="info-card tip-box">
            <h4><i class="fas fa-lightbulb icon"></i>今日贴士</h4>
            <p>1. 第一天行程较轻松，主要适应南京节奏</p>
            <p>2. 老门东和夫子庙相距很近，步行约5分钟</p>
            <p>3. 夏季室外活动建议携带防晒用品及遮阳伞</p>
            <p>4. 如父母体力不足，可选择提前返回酒店休息</p>
        </div>

    <div class="day-header">
        <i class="fas fa-calendar-day"></i>第二天 (6月16日 周日)：明清文化探索
    </div>
    <table class="itinerary-table no-break">
        <tr>
            <th class="time-col">时间</th>
            <th class="activity-col">活动</th>
            <th class="details-col">详情</th>
        </tr>
        <tr>
            <td>08:00-09:00</td>
            <td>早餐</td>
            <td>
                <p>酒店早餐或附近早餐店</p>
                <p>推荐：南京大排档早点铺（鸡汁灌汤包、牛肉锅贴）</p>
            </td>
        </tr>
        <tr>
            <td>09:30-12:30</td>
            <td>南京博物院</td>
            <td>
                <p>开放时间：9:00-17:00（周一闭馆）</p>
                <p>门票：免费（需提前预约）</p>
                <p>重点推荐：历史馆、特展馆（明清文物展区）</p>
                <p>设施：有电梯、休息区，适合老年人</p>
            </td>
        </tr>
        <tr>
            <td>12:30-14:00</td>
            <td>午餐</td>
            <td>
                <p>推荐：金陵风味餐厅（博物院附近）</p>
                <p>品尝：清炖狮子头、松鼠桂鱼（不辣）</p>
                <p>人均：70-90元</p>
            </td>
        </tr>
        <tr>
            <td>14:30-17:00</td>
            <td>总统府</td>
            <td>
                <p>开放时间：8:30-17:00</p>
                <p>门票：40元（老人半价）</p>
                <p>重点：明清两朝历史变迁，中国近代史展览</p>
                <p>亮点：中国最大的盆景园</p>
                <p>交通：从博物院步行约15分钟或乘坐2站公交</p>
            </td>
        </tr>
        <tr>
            <td>17:30-19:00</td>
            <td>晚餐</td>
            <td>
                <p>推荐：南京大牌档（新街口店）</p>
                <p>品尝：什锦菜饭、鸭油烧饼、桂花糯米藕（不辣）</p>
                <p>人均：80-100元</p>
            </td>
        </tr>
        <tr>
            <td>19:30-</td>
            <td>休息</td>
            <td>
                <p>返回酒店休息</p>
                <p>如精力充沛可散步至1912街区（欧式风情商业区）</p>
            </td>
        </tr>
    </table>

        <div class="info-card tip-box">
            <h4><i class="fas fa-lightbulb icon"></i>今日贴士</h4>
            <p>1. 南京博物院面积大，建议优先参观历史馆和特展馆</p>
            <p>2. 博物院提供免费讲解，可通过官方小程序预约</p>
            <p>3. 总统府内步行路线较长，建议选择主要景点参观</p>
            <p>4. 老人累了可在园内多休息，不必赶时间</p>
        </div>

    <div class="page-break"></div>

    <div class="day-header">
        <i class="fas fa-calendar-day"></i>第三天 (6月17日 周一)：紫金山皇陵之旅
    </div>
    <table class="itinerary-table no-break">
        <tr>
            <th class="time-col">时间</th>
            <th class="activity-col">活动</th>
            <th class="details-col">详情</th>
        </tr>
        <tr>
            <td>08:00-09:00</td>
            <td>早餐</td>
            <td>
                <p>酒店早餐</p>
            </td>
        </tr>
        <tr>
            <td>09:30-12:00</td>
            <td>明孝陵</td>
            <td>
                <p>开放时间：8:30-17:00</p>
                <p>门票：70元（老人半价）</p>
                <p>重点：神道石像生，展示明代雕刻艺术</p>
                <p>交通：乘坐地铁2号线至玄武门站，换乘游5路公交</p>
                <p>建议乘坐景区电瓶车减少步行（约20元/人）</p>
            </td>
        </tr>
        <tr>
            <td>12:00-13:30</td>
            <td>午餐</td>
            <td>
                <p>推荐：紫金山庄餐厅（景区附近）</p>
                <p>品尝：南京菜饭、开洋干丝（清淡）</p>
                <p>人均：60-80元</p>
            </td>
        </tr>
        <tr>
            <td>14:00-16:30</td>
            <td>美龄宫/中山陵</td>
            <td>
                <p>二选一，根据父母体力情况决定：</p>
                <p><strong>美龄宫</strong>：台阶较少，更适合老年人</p>
                <p>门票：40元（老人半价），欣赏民国建筑和宋氏三姐妹故事</p>
                <p><strong>中山陵</strong>：需爬约400台阶，体力较好可选</p>
                <p>门票：免费，孙中山陵墓，中国现代建筑经典</p>
            </td>
        </tr>
        <tr>
            <td>17:00-18:30</td>
            <td>返回市区</td>
            <td>
                <p>乘坐游5路公交返回玄武门站，换乘地铁</p>
            </td>
        </tr>
        <tr>
            <td>18:30-20:00</td>
            <td>晚餐</td>
            <td>
                <p>推荐：桂满陇（新街口店）</p>
                <p>品尝：东坡肉、虾籽烧卖（清淡不辣）</p>
                <p>人均：100-120元</p>
            </td>
        </tr>
    </table>

        <div class="info-card tip-box">
            <h4><i class="fas fa-lightbulb icon"></i>今日贴士</h4>
            <p>1. 今天行程体力消耗较大，注意休息与补水</p>
            <p>2. 紫金山景区面积广，景点之间距离较远，建议利用景区交通工具</p>
            <p>3. 如遇高温天气，可调整为上午美龄宫/中山陵，下午明孝陵</p>
            <p>4. 准备防晒和雨具，山区天气多变</p>
        </div>

    <div class="day-header">
        <i class="fas fa-calendar-day"></i>第四天 (6月18日 周二)：江南园林与历史探索
    </div>
    <table class="itinerary-table no-break">
        <tr>
            <th class="time-col">时间</th>
            <th class="activity-col">活动</th>
            <th class="details-col">详情</th>
        </tr>
        <tr>
            <td>08:00-09:00</td>
            <td>早餐</td>
            <td>
                <p>酒店早餐</p>
            </td>
        </tr>
        <tr>
            <td>09:30-11:30</td>
            <td>瞻园</td>
            <td>
                <p>开放时间：8:30-17:30</p>
                <p>门票：40元（老人半价）</p>
                <p>明代私家园林，规模适中，适合慢慢欣赏</p>
                <p>亮点：假山、湖泊、回廊和古树</p>
                <p>交通：乘坐地铁3号线至大行宫站</p>
            </td>
        </tr>
        <tr>
            <td>12:00-13:30</td>
            <td>午餐</td>
            <td>
                <p>推荐：狮子桥美食街-知味观</p>
                <p>品尝：蟹黄汤包、清炖蹄膀（不辣）</p>
                <p>人均：70-90元</p>
                <p>交通：从瞻园乘坐地铁3号线至新街口站</p>
            </td>
        </tr>
        <tr>
            <td>14:00-16:30</td>
            <td>江宁织造博物馆</td>
            <td>
                <p>开放时间：9:00-16:30（周一闭馆）</p>
                <p>门票：免费</p>
                <p>清代江南丝织业中心，了解明清丝绸历史</p>
                <p>馆内讲解详尽，对明清历史感兴趣的父母会喜欢</p>
                <p>交通：从狮子桥乘坐地铁3号线至武定门站</p>
            </td>
        </tr>
        <tr>
            <td>17:00-18:30</td>
            <td>休息</td>
            <td>
                <p>返回酒店休息</p>
            </td>
        </tr>
        <tr>
            <td>18:30-20:00</td>
            <td>晚餐</td>
            <td>
                <p>推荐：同曦鸭血粉丝汤（夫子庙店）</p>
                <p>品尝：鸭血粉丝汤、鸭油酥烧饼（清淡不辣）</p>
                <p>人均：60-80元</p>
            </td>
        </tr>
    </table>

        <div class="info-card tip-box">
            <h4><i class="fas fa-lightbulb icon"></i>今日贴士</h4>
            <p>1. 瞻园与江宁织造博物馆规模较小，参观节奏可放缓</p>
            <p>2. 狮子桥美食街人流量大，用餐建议避开高峰期</p>
            <p>3. 江宁织造博物馆有精彩的织锦演示，可提前咨询时间</p>
            <p>4. 如父母有兴趣，可在夫子庙附近散步购买纪念品</p>
        </div>

    <div class="page-break"></div>

    <div class="day-header">
        <i class="fas fa-calendar-day"></i>第五天 (6月19日 周三)：湖光山色与返程
    </div>
    <table class="itinerary-table no-break">
        <tr>
            <th class="time-col">时间</th>
            <th class="activity-col">活动</th>
            <th class="details-col">详情</th>
        </tr>
        <tr>
            <td>08:00-09:00</td>
            <td>早餐&退房</td>
            <td>
                <p>酒店早餐，办理退房</p>
                <p>可将行李寄存在酒店前台</p>
            </td>
        </tr>
        <tr>
            <td>09:30-12:00</td>
            <td>玄武湖公园</td>
            <td>
                <p>开放时间：全天</p>
                <p>门票：免费</p>
                <p>环湖漫步，欣赏湖光山色</p>
                <p>可租船游湖（80元/船），避免长距离步行</p>
                <p>交通：乘坐地铁1号线至玄武湖站</p>
            </td>
        </tr>
        <tr>
            <td>12:00-13:30</td>
            <td>午餐</td>
            <td>
                <p>推荐：桂满陇（玄武湖店）</p>
                <p>品尝：经典淮扬菜（蟹粉狮子头、清蒸鲈鱼）</p>
                <p>人均：100-120元</p>
            </td>
        </tr>
        <tr>
            <td>14:00-14:30</td>
            <td>返回酒店取行李</td>
            <td>
                <p>根据高铁时间安排返回酒店取行李</p>
            </td>
        </tr>
        <tr>
            <td>15:04-16:01</td>
            <td>乘坐高铁返程</td>
            <td>
                <p>南京南站→合肥南站</p>
                <p>G411次列车</p>
                <p>建议提前1小时到达车站</p>
            </td>
        </tr>
    </table>

        <div class="info-card tip-box">
            <h4><i class="fas fa-lightbulb icon"></i>今日贴士</h4>
            <p>1. 玄武湖公园面积大，可根据体力选择游览区域</p>
            <p>2. 乘船游湖是减轻体力消耗的好方式</p>
            <p>3. 返程前确认未遗留物品</p>
            <p>4. 如高铁时间充裕，可在南京南站周边商场购物</p>
        </div>

    <div class="section-title">
        <i class="fas fa-utensils"></i>特色美食推荐（清淡口味）
    </div>
    <div class="food-section">
        <div class="food-card">
            <h4>盐水鸭</h4>
            <p>南京代表性菜品，咸鲜不辣，肉质鲜嫩</p>
            <p>推荐店铺：大牌档、南京全鸭汤</p>
            <p>价格：60-80元/份</p>
        </div>
        <div class="food-card">
            <h4>鸭血粉丝汤</h4>
            <p>南京名小吃，清淡爽口，营养丰富</p>
            <p>推荐店铺：同曦鸭血粉丝汤、大牌档</p>
            <p>价格：25-40元/碗</p>
        </div>
        <div class="food-card">
            <h4>蟹粉狮子头</h4>
            <p>淮扬菜代表，肉质松软，鲜香不辣</p>
            <p>推荐店铺：桂满陇、南京大牌档</p>
            <p>价格：50-80元/份</p>
        </div>
        <div class="food-card">
            <h4>桂花拉糕/糯米藕</h4>
            <p>传统甜点，桂花清香，甜而不腻</p>
            <p>推荐店铺：夫子庙小吃店、知味观</p>
            <p>价格：15-30元/份</p>
        </div>
    </div>

        <div class="section-title">
            <i class="fas fa-suitcase"></i>行李清单与注意事项
        </div>
        <div class="info-card tip-box">
            <h4><i class="fas fa-tshirt icon"></i>服装与日用品</h4>
            <ul class="checklist">
                <li>舒适轻便的衣物（6月南京温热多雨）</li>
                <li>舒适平底鞋（景区游览需要大量步行）</li>
                <li>遮阳伞（防晒/防雨两用）</li>
                <li>防晒霜、墨镜</li>
                <li>常用药品（老人日常药物、消炎药、止泻药等）</li>
                <li>老花镜、充电宝</li>
            </ul>
        </div>

        <div class="info-card tip-box">
            <h4><i class="fas fa-exclamation-triangle icon"></i>重要提示</h4>
            <ul class="checklist">
                <li>提前3-7天预约南京博物院、总统府等需预约的景点</li>
                <li>随身携带老人证、医保卡等证件（景点门票优惠）</li>
                <li>准备充足现金和移动支付方式</li>
                <li>酒店周边药店、便利店位置提前了解</li>
                <li>下载高德地图/南京地铁APP便于出行</li>
                <li>景区内留意休息区位置，及时休息</li>
            </ul>
        </div>

    <div class="section-title">
        <i class="fas fa-coins"></i>预算估算
    </div>
    <table class="budget-table">
        <tr>
            <th>项目</th>
            <th>明细</th>
            <th>金额(元)</th>
        </tr>
        <tr>
            <td>交通费用</td>
            <td>高铁往返票（3人）：600<br>市内交通（地铁/公交/出租车）：300</td>
            <td>900</td>
        </tr>
        <tr>
            <td>住宿费用</td>
            <td>连锁酒店4晚（400-500元/晚）</td>
            <td>1,600</td>
        </tr>
        <tr>
            <td>餐饮费用</td>
            <td>正餐（3人5天，人均70元/餐）：900<br>小吃、饮料：300</td>
            <td>1,200</td>
        </tr>
        <tr>
            <td>门票及游览</td>
            <td>景点门票（已考虑老人优惠）</td>
            <td>500</td>
        </tr>
        <tr>
            <td>其他费用</td>
            <td>购物、纪念品、不可预见费用</td>
            <td>300</td>
        </tr>
        <tr class="budget-total">
            <td colspan="2">总计</td>
            <td>4,500</td>
        </tr>
    </table>

        <div class="section-title">
            <i class="fas fa-phone-alt"></i>紧急联系信息
        </div>
        <div class="contact-box">
            <div class="contact-section">
                <h4><i class="fas fa-ambulance icon"></i>紧急服务</h4>
                <p><strong>紧急救援：</strong> 120</p>
                <p><strong>报警电话：</strong> 110</p>
                <p><strong>消防救援：</strong> 119</p>
            </div>
            <div class="contact-section">
                <h4><i class="fas fa-info-circle icon"></i>旅游服务</h4>
                <p><strong>南京旅游咨询：</strong> 025-12301</p>
                <p><strong>南京交通热线：</strong> 025-96196</p>
                <p><strong>酒店电话：</strong> 预订后填写</p>
            </div>
        </div>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);"></div>
                <span>日程安排</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: linear-gradient(135deg, #fff8e1 0%, #ffeaa7 100%);"></div>
                <span>重要提示</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);"></div>
                <span>交通信息</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);"></div>
                <span>餐饮信息</span>
            </div>
        </div>

        <button class="print-button" onclick="window.print()">
            <i class="fas fa-print"></i>打印行程表
        </button>
    </div>
</body>
</html>
